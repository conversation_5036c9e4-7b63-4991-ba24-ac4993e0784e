package rules

// CheckRule 检查规则结构
type CheckRule struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Category    string `json:"category"`
	Level       string `json:"level"`
	Description string `json:"description"`
	Command     string `json:"command"`
	Expected    string `json:"expected"`
	Severity    string `json:"severity"`
	Suggestion  string `json:"suggestion"`
}

// 等保基线检查规则定义
var (
	// 账户安全类规则
	AccountSecurityRules = []CheckRule{
		{
			ID:          "L2_ACCOUNT_001",
			Name:        "检查空密码账户",
			Category:    "账户安全",
			Level:       "level2",
			Description: "检查系统中是否存在空密码账户",
			Command:     "awk -F: '($2 == \"\") {print $1}' /etc/shadow",
			Expected:    "",
			Severity:    "high",
			Suggestion:  "为所有账户设置强密码，删除或禁用空密码账户",
		},
		{
			ID:          "L2_ACCOUNT_002",
			Name:        "检查密码最小长度",
			Category:    "账户安全",
			Level:       "level2",
			Description: "检查密码最小长度配置",
			Command:     "grep '^PASS_MIN_LEN' /etc/login.defs | awk '{print $2}'",
			Expected:    "8",
			Severity:    "medium",
			Suggestion:  "在/etc/login.defs中设置PASS_MIN_LEN为8或更高",
		},
		{
			ID:          "L2_ACCOUNT_003",
			Name:        "检查密码最大有效期",
			Category:    "账户安全",
			Level:       "level2",
			Description: "检查密码最大有效期配置",
			Command:     "grep '^PASS_MAX_DAYS' /etc/login.defs | awk '{print $2}'",
			Expected:    "90",
			Severity:    "medium",
			Suggestion:  "在/etc/login.defs中设置PASS_MAX_DAYS为90天",
		},
		{
			ID:          "L3_ACCOUNT_001",
			Name:        "检查账户锁定策略",
			Category:    "账户安全",
			Level:       "level3",
			Description: "检查账户锁定策略配置",
			Command:     "grep 'pam_faillock' /etc/pam.d/system-auth",
			Expected:    "pam_faillock.so",
			Severity:    "medium",
			Suggestion:  "配置PAM账户锁定策略，防止暴力破解",
		},
		{
			ID:          "L3_ACCOUNT_002",
			Name:        "检查密码复杂度",
			Category:    "账户安全",
			Level:       "level3",
			Description: "检查密码复杂度要求配置",
			Command:     "grep 'pam_pwquality' /etc/pam.d/system-auth",
			Expected:    "pam_pwquality.so",
			Severity:    "medium",
			Suggestion:  "配置密码复杂度要求，包含大小写字母、数字和特殊字符",
		},
	}

	// 系统配置类规则
	SystemConfigRules = []CheckRule{
		{
			ID:          "L2_SYSTEM_001",
			Name:        "检查SSH root登录",
			Category:    "系统配置",
			Level:       "level2",
			Description: "检查SSH是否禁用root登录",
			Command:     "grep '^PermitRootLogin' /etc/ssh/sshd_config | awk '{print $2}'",
			Expected:    "no",
			Severity:    "high",
			Suggestion:  "在/etc/ssh/sshd_config中设置PermitRootLogin no",
		},
		{
			ID:          "L2_SYSTEM_002",
			Name:        "检查SSH协议版本",
			Category:    "系统配置",
			Level:       "level2",
			Description: "检查SSH协议版本配置",
			Command:     "grep '^Protocol' /etc/ssh/sshd_config | awk '{print $2}'",
			Expected:    "2",
			Severity:    "high",
			Suggestion:  "在/etc/ssh/sshd_config中设置Protocol 2",
		},
		{
			ID:          "L2_SYSTEM_003",
			Name:        "检查系统内核版本",
			Category:    "系统配置",
			Level:       "level2",
			Description: "检查系统内核版本是否为最新",
			Command:     "uname -r",
			Expected:    "",
			Severity:    "medium",
			Suggestion:  "定期更新系统内核到最新稳定版本",
		},
		{
			ID:          "L3_SYSTEM_001",
			Name:        "检查系统启动项",
			Category:    "系统配置",
			Level:       "level3",
			Description: "检查系统启动项安全配置",
			Command:     "systemctl list-unit-files --type=service --state=enabled | wc -l",
			Expected:    "",
			Severity:    "low",
			Suggestion:  "定期审查系统启动项，禁用不必要的服务",
		},
	}

	// 网络安全类规则
	NetworkSecurityRules = []CheckRule{
		{
			ID:          "L2_NETWORK_001",
			Name:        "检查防火墙状态",
			Category:    "网络安全",
			Level:       "level2",
			Description: "检查防火墙是否启用",
			Command:     "systemctl is-active firewalld 2>/dev/null || systemctl is-active ufw 2>/dev/null || echo 'inactive'",
			Expected:    "active",
			Severity:    "high",
			Suggestion:  "启用系统防火墙服务（firewalld或ufw）",
		},
		{
			ID:          "L2_NETWORK_002",
			Name:        "检查开放端口",
			Category:    "网络安全",
			Level:       "level2",
			Description: "检查系统开放的网络端口",
			Command:     "ss -tuln | grep LISTEN | wc -l",
			Expected:    "",
			Severity:    "medium",
			Suggestion:  "关闭不必要的网络端口，只保留业务必需端口",
		},
		{
			ID:          "L3_NETWORK_001",
			Name:        "检查网络参数配置",
			Category:    "网络安全",
			Level:       "level3",
			Description: "检查网络安全参数配置",
			Command:     "sysctl net.ipv4.ip_forward",
			Expected:    "net.ipv4.ip_forward = 0",
			Severity:    "medium",
			Suggestion:  "禁用IP转发功能，防止系统被用作路由器",
		},
	}

	// 文件系统类规则
	FileSystemRules = []CheckRule{
		{
			ID:          "L2_FILE_001",
			Name:        "检查关键文件权限",
			Category:    "文件系统",
			Level:       "level2",
			Description: "检查/etc/passwd文件权限",
			Command:     "stat -c '%a' /etc/passwd",
			Expected:    "644",
			Severity:    "high",
			Suggestion:  "设置/etc/passwd文件权限为644",
		},
		{
			ID:          "L2_FILE_002",
			Name:        "检查shadow文件权限",
			Category:    "文件系统",
			Level:       "level2",
			Description: "检查/etc/shadow文件权限",
			Command:     "stat -c '%a' /etc/shadow",
			Expected:    "000",
			Severity:    "high",
			Suggestion:  "设置/etc/shadow文件权限为000或600",
		},
		{
			ID:          "L3_FILE_001",
			Name:        "检查临时目录权限",
			Category:    "文件系统",
			Level:       "level3",
			Description: "检查/tmp目录权限配置",
			Command:     "stat -c '%a' /tmp",
			Expected:    "1777",
			Severity:    "medium",
			Suggestion:  "设置/tmp目录权限为1777（粘滞位）",
		},
	}

	// 日志审计类规则
	AuditRules = []CheckRule{
		{
			ID:          "L2_AUDIT_001",
			Name:        "检查系统日志服务",
			Category:    "日志审计",
			Level:       "level2",
			Description: "检查系统日志服务是否启用",
			Command:     "systemctl is-active rsyslog",
			Expected:    "active",
			Severity:    "high",
			Suggestion:  "启用rsyslog服务，确保系统日志正常记录",
		},
		{
			ID:          "L3_AUDIT_001",
			Name:        "检查审计服务",
			Category:    "日志审计",
			Level:       "level3",
			Description: "检查审计服务是否启用",
			Command:     "systemctl is-active auditd",
			Expected:    "active",
			Severity:    "high",
			Suggestion:  "启用auditd服务，记录系统安全事件",
		},
		{
			ID:          "L3_AUDIT_002",
			Name:        "检查审计规则",
			Category:    "日志审计",
			Level:       "level3",
			Description: "检查审计规则配置",
			Command:     "auditctl -l | wc -l",
			Expected:    "",
			Severity:    "medium",
			Suggestion:  "配置适当的审计规则，监控关键系统事件",
		},
	}

	// 安全更新类规则
	UpdateRules = []CheckRule{
		{
			ID:          "L2_UPDATE_001",
			Name:        "检查系统更新",
			Category:    "安全更新",
			Level:       "level2",
			Description: "检查系统是否有可用更新",
			Command:     "yum check-update 2>/dev/null | grep -v '^$' | wc -l || apt list --upgradable 2>/dev/null | wc -l",
			Expected:    "0",
			Severity:    "medium",
			Suggestion:  "定期安装系统安全更新",
		},
		{
			ID:          "L3_UPDATE_001",
			Name:        "检查自动更新配置",
			Category:    "安全更新",
			Level:       "level3",
			Description: "检查自动更新服务配置",
			Command:     "systemctl is-enabled yum-cron 2>/dev/null || systemctl is-enabled unattended-upgrades 2>/dev/null || echo 'disabled'",
			Expected:    "enabled",
			Severity:    "low",
			Suggestion:  "配置自动安全更新服务",
		},
	}
)

// RuleManager 规则管理器
type RuleManager struct {
	rules map[string]map[string][]CheckRule
}

// NewRuleManager 创建规则管理器
func NewRuleManager() *RuleManager {
	rm := &RuleManager{
		rules: make(map[string]map[string][]CheckRule),
	}
	rm.initRules()
	return rm
}

// initRules 初始化所有规则
func (rm *RuleManager) initRules() {
	// 等保二级规则
	level2 := make(map[string][]CheckRule)
	level2["账户安全"] = AccountSecurityRules[:3] // 前3个规则
	level2["系统配置"] = SystemConfigRules[:3]   // 前3个规则
	level2["网络安全"] = NetworkSecurityRules[:2] // 前2个规则
	level2["文件系统"] = FileSystemRules[:2]     // 前2个规则
	level2["日志审计"] = AuditRules[:1]          // 前1个规则
	level2["安全更新"] = UpdateRules[:1]         // 前1个规则

	// 等保三级规则（包含二级所有规则）
	level3 := make(map[string][]CheckRule)
	level3["账户安全"] = AccountSecurityRules    // 所有账户安全规则
	level3["系统配置"] = SystemConfigRules      // 所有系统配置规则
	level3["网络安全"] = NetworkSecurityRules   // 所有网络安全规则
	level3["文件系统"] = FileSystemRules        // 所有文件系统规则
	level3["日志审计"] = AuditRules             // 所有日志审计规则
	level3["安全更新"] = UpdateRules            // 所有安全更新规则

	rm.rules["level2"] = level2
	rm.rules["level3"] = level3
}

// GetRulesByLevel 根据等级获取所有规则
func (rm *RuleManager) GetRulesByLevel(level string) []CheckRule {
	var allRules []CheckRule
	if levelRules, exists := rm.rules[level]; exists {
		for _, categoryRules := range levelRules {
			allRules = append(allRules, categoryRules...)
		}
	}
	return allRules
}

// GetRulesByCategory 根据等级和分类获取规则
func (rm *RuleManager) GetRulesByCategory(level, category string) []CheckRule {
	if levelRules, exists := rm.rules[level]; exists {
		if categoryRules, exists := levelRules[category]; exists {
			return categoryRules
		}
	}
	return []CheckRule{}
}

// GetAllCategories 获取指定等级的所有分类
func (rm *RuleManager) GetAllCategories(level string) []string {
	var categories []string
	if levelRules, exists := rm.rules[level]; exists {
		for category := range levelRules {
			categories = append(categories, category)
		}
	}
	return categories
}

// GetRuleByID 根据ID获取规则
func (rm *RuleManager) GetRuleByID(ruleID string) *CheckRule {
	for _, levelRules := range rm.rules {
		for _, categoryRules := range levelRules {
			for _, rule := range categoryRules {
				if rule.ID == ruleID {
					return &rule
				}
			}
		}
	}
	return nil
}

// GetRuleStats 获取规则统计信息
func (rm *RuleManager) GetRuleStats() map[string]interface{} {
	stats := make(map[string]interface{})
	
	for level, levelRules := range rm.rules {
		levelStats := make(map[string]int)
		totalRules := 0
		
		for category, categoryRules := range levelRules {
			levelStats[category] = len(categoryRules)
			totalRules += len(categoryRules)
		}
		
		levelStats["total"] = totalRules
		stats[level] = levelStats
	}
	
	return stats
}
