package main

import (
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
	"fyne.io/fyne/v2"
)

// MainApp 主应用结构
type MainApp struct {
	app    fyne.App
	window fyne.Window
	
	// 主要功能页面
	serverPage   *ServerManagementPage
	checkPage    *CheckFunctionPage
	reportPage   *ReportManagementPage
	settingsPage *SettingsPage
	
	// 当前显示的内容
	content *container.Border
}

// ServerManagementPage 服务器管理页面
type ServerManagementPage struct {
	container *container.TabContainer
	
	// 单主机管理
	singleHostTab *container.VBox
	
	// 多主机管理
	multiHostTab *container.VBox
}

// CheckFunctionPage 检查功能页面
type CheckFunctionPage struct {
	container *container.VBox
	
	// 步骤1: 选择目标
	targetSelection *widget.RadioGroup
	selectedServers *widget.Label
	
	// 步骤2: 选择模板
	templateSelection *widget.RadioGroup
	templateInfo      *widget.Label
	
	// 步骤3: 执行检查
	startButton    *widget.Button
	progressBars   []*widget.ProgressBar
	statusLabel    *widget.Label
}

// ReportManagementPage 报告管理页面
type ReportManagementPage struct {
	container *container.VBox
	
	// 历史报告列表
	reportList *widget.List
	
	// 报告详情
	reportDetail *container.VBox
	
	// 导出按钮
	exportButtons *container.HBox
}

// SettingsPage 设置页面
type SettingsPage struct {
	container *container.TabContainer
	
	// 各种设置选项
	basicSettings  *container.VBox
	checkSettings  *container.VBox
	reportSettings *container.VBox
	uiSettings     *container.VBox
}

// NewMainApp 创建主应用
func NewMainApp() *MainApp {
	myApp := app.New()
	myWindow := myApp.NewWindow("Linux等保基线核查工具")
	myWindow.Resize(fyne.NewSize(1200, 800))
	
	mainApp := &MainApp{
		app:    myApp,
		window: myWindow,
	}
	
	mainApp.initPages()
	mainApp.setupUI()
	
	return mainApp
}

// initPages 初始化各个功能页面
func (m *MainApp) initPages() {
	m.serverPage = m.createServerManagementPage()
	m.checkPage = m.createCheckFunctionPage()
	m.reportPage = m.createReportManagementPage()
	m.settingsPage = m.createSettingsPage()
}

// createServerManagementPage 创建服务器管理页面
func (m *MainApp) createServerManagementPage() *ServerManagementPage {
	page := &ServerManagementPage{}
	
	// 单主机管理标签页
	page.singleHostTab = container.NewVBox(
		widget.NewCard("服务器信息", "",
			container.NewVBox(
				container.NewHBox(
					widget.NewLabel("名称:"),
					widget.NewEntry(),
					widget.NewLabel("IP:"),
					widget.NewEntry(),
				),
				container.NewHBox(
					widget.NewLabel("端口:"),
					widget.NewEntry(),
					widget.NewLabel("用户:"),
					widget.NewEntry(),
				),
				container.NewHBox(
					widget.NewLabel("认证:"),
					widget.NewRadioGroup([]string{"密码", "密钥"}, nil),
				),
				container.NewHBox(
					widget.NewLabel("密码:"),
					widget.NewPasswordEntry(),
				),
				container.NewHBox(
					widget.NewButton("测试连接", nil),
					widget.NewButton("保存", nil),
					widget.NewButton("删除", nil),
				),
			),
		),
	)
	
	// 多主机管理标签页
	serverList := widget.NewList(
		func() int { return 3 }, // 示例数据
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewCheck("", nil),
				widget.NewLabel("服务器名称"),
				widget.NewLabel("IP地址"),
				widget.NewLabel("状态"),
				widget.NewButton("编辑", nil),
				widget.NewButton("删除", nil),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			// 更新列表项数据
		},
	)
	
	page.multiHostTab = container.NewVBox(
		container.NewHBox(
			widget.NewButton("添加", nil),
			widget.NewButton("导入", nil),
			widget.NewButton("批量测试", nil),
		),
		serverList,
	)
	
	page.container = container.NewTabContainer(
		container.NewTabItem("单主机管理", page.singleHostTab),
		container.NewTabItem("多主机管理", page.multiHostTab),
	)
	
	return page
}

// createCheckFunctionPage 创建检查功能页面
func (m *MainApp) createCheckFunctionPage() *CheckFunctionPage {
	page := &CheckFunctionPage{}
	
	// 步骤1: 选择检查目标
	page.targetSelection = widget.NewRadioGroup([]string{"单主机检查", "多主机检查"}, nil)
	page.selectedServers = widget.NewLabel("已选择0台服务器")
	
	step1 := widget.NewCard("步骤1: 选择检查目标", "",
		container.NewVBox(
			page.targetSelection,
			page.selectedServers,
			widget.NewButton("重新选择", nil),
		),
	)
	
	// 步骤2: 选择检查模板
	page.templateSelection = widget.NewRadioGroup([]string{"等保二级基线", "等保三级基线", "自定义模板"}, nil)
	page.templateInfo = widget.NewLabel("检查项目: 账户安全(5项) 系统配置(4项) 网络安全(3项)...")
	
	step2 := widget.NewCard("步骤2: 选择检查模板", "",
		container.NewVBox(
			page.templateSelection,
			page.templateInfo,
		),
	)
	
	// 步骤3: 开始检查
	page.startButton = widget.NewButton("开始检查", nil)
	page.statusLabel = widget.NewLabel("就绪")
	
	// 进度条示例
	progress1 := widget.NewProgressBar()
	progress1.SetValue(1.0) // 100%
	progress2 := widget.NewProgressBar()
	progress2.SetValue(0.6) // 60%
	progress3 := widget.NewProgressBar()
	progress3.SetValue(0.2) // 20%
	
	step3 := widget.NewCard("步骤3: 开始检查", "",
		container.NewVBox(
			container.NewHBox(
				page.startButton,
				widget.NewButton("保存配置", nil),
				widget.NewButton("加载配置", nil),
			),
			widget.NewSeparator(),
			widget.NewLabel("检查进度:"),
			container.NewHBox(widget.NewLabel("服务器1:"), progress1, widget.NewLabel("100% (20/20)")),
			container.NewHBox(widget.NewLabel("服务器2:"), progress2, widget.NewLabel("60% (12/20)")),
			container.NewHBox(widget.NewLabel("服务器3:"), progress3, widget.NewLabel("20% (4/20)")),
			page.statusLabel,
			container.NewHBox(
				widget.NewButton("暂停", nil),
				widget.NewButton("停止", nil),
				widget.NewButton("查看详细日志", nil),
			),
		),
	)
	
	page.container = container.NewVBox(step1, step2, step3)
	return page
}

// createReportManagementPage 创建报告管理页面
func (m *MainApp) createReportManagementPage() *ReportManagementPage {
	page := &ReportManagementPage{}
	
	// 历史报告列表
	page.reportList = widget.NewList(
		func() int { return 3 }, // 示例数据
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("2024-08-20 14:30"),
				widget.NewLabel("等保三级"),
				widget.NewLabel("3台服务器"),
				widget.NewButton("查看", nil),
				widget.NewButton("导出", nil),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			// 更新列表项数据
		},
	)
	
	// 报告详情
	page.reportDetail = container.NewVBox(
		widget.NewLabel("检查概要: 通过率 85% (51/60项)"),
		widget.NewLabel("高风险: 3项  中风险: 6项  低风险: 0项"),
		widget.NewSeparator(),
		widget.NewLabel("服务器详情:"),
		widget.NewLabel("• 服务器1: 通过 18/20项  🟡中风险"),
		widget.NewLabel("• 服务器2: 通过 16/20项  🔴高风险"),
		widget.NewLabel("• 服务器3: 通过 17/20项  🟡中风险"),
	)
	
	// 导出按钮
	page.exportButtons = container.NewHBox(
		widget.NewButton("导出HTML", nil),
		widget.NewButton("导出PDF", nil),
		widget.NewButton("导出Excel", nil),
		widget.NewButton("打印", nil),
	)
	
	page.container = container.NewVBox(
		widget.NewCard("检查历史", "", 
			container.NewVBox(
				container.NewHBox(widget.NewLabel("检查历史"), widget.NewButton("刷新", nil)),
				page.reportList,
			),
		),
		widget.NewCard("报告详情", "",
			container.NewVBox(
				page.reportDetail,
				page.exportButtons,
			),
		),
	)
	
	return page
}

// createSettingsPage 创建设置页面
func (m *MainApp) createSettingsPage() *SettingsPage {
	page := &SettingsPage{}
	
	// 基本设置
	page.basicSettings = container.NewVBox(
		container.NewHBox(widget.NewLabel("SSH连接超时:"), widget.NewEntry(), widget.NewLabel("秒")),
		container.NewHBox(widget.NewLabel("并发连接数:"), widget.NewEntry(), widget.NewLabel("个")),
		widget.NewCheck("自动保存结果", nil),
		widget.NewCheck("每日备份数据库", nil),
	)
	
	// 检查配置
	page.checkSettings = container.NewVBox(
		container.NewHBox(widget.NewLabel("默认检查模板:"), widget.NewSelect([]string{"等保二级基线", "等保三级基线"}, nil)),
		container.NewHBox(widget.NewLabel("检查失败重试:"), widget.NewEntry(), widget.NewLabel("次")),
		widget.NewCheck("跳过无法连接的服务器", nil),
	)
	
	// 报告设置
	page.reportSettings = container.NewVBox(
		container.NewHBox(widget.NewLabel("默认导出格式:"), widget.NewSelect([]string{"HTML", "PDF", "Excel"}, nil)),
		widget.NewCheck("包含详细日志", nil),
		container.NewHBox(widget.NewLabel("报告保存路径:"), widget.NewEntry(), widget.NewButton("浏览", nil)),
	)
	
	// 界面设置
	page.uiSettings = container.NewVBox(
		container.NewHBox(widget.NewLabel("主题:"), widget.NewRadioGroup([]string{"浅色主题", "深色主题", "自动"}, nil)),
		container.NewHBox(widget.NewLabel("语言:"), widget.NewSelect([]string{"中文", "English"}, nil)),
	)
	
	page.container = container.NewTabContainer(
		container.NewTabItem("基本设置", page.basicSettings),
		container.NewTabItem("检查配置", page.checkSettings),
		container.NewTabItem("报告设置", page.reportSettings),
		container.NewTabItem("界面设置", page.uiSettings),
	)
	
	return page
}

// setupUI 设置主界面
func (m *MainApp) setupUI() {
	// 顶部导航按钮
	serverBtn := widget.NewButton("🖥️ 服务器管理", func() {
		m.showPage("server")
	})
	checkBtn := widget.NewButton("🔍 检查功能", func() {
		m.showPage("check")
	})
	reportBtn := widget.NewButton("📊 报告管理", func() {
		m.showPage("report")
	})
	settingsBtn := widget.NewButton("⚙️ 设置", func() {
		m.showPage("settings")
	})
	
	topNav := container.NewHBox(serverBtn, checkBtn, reportBtn, settingsBtn)
	
	// 状态栏
	statusBar := container.NewHBox(
		widget.NewLabel("状态: 就绪"),
		widget.NewLabel("服务器: 0台在线"),
		widget.NewLabel("最后检查: 无"),
		widget.NewLabel("版本: v1.0"),
	)
	
	// 主要内容区域
	m.content = container.NewBorder(
		topNav,     // top
		statusBar,  // bottom
		nil,        // left
		nil,        // right
		m.serverPage.container, // center - 默认显示服务器管理页面
	)
	
	m.window.SetContent(m.content)
}

// showPage 切换显示的页面
func (m *MainApp) showPage(page string) {
	var content fyne.CanvasObject
	
	switch page {
	case "server":
		content = m.serverPage.container
	case "check":
		content = m.checkPage.container
	case "report":
		content = m.reportPage.container
	case "settings":
		content = m.settingsPage.container
	default:
		content = m.serverPage.container
	}
	
	// 更新中心内容
	m.content.Objects[4] = content
	m.content.Refresh()
}

// Run 运行应用
func (m *MainApp) Run() {
	m.window.ShowAndRun()
}

func main() {
	app := NewMainApp()
	app.Run()
}
