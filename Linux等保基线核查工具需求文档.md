# Linux等保基线核查工具需求文档

## 1. 项目概述

### 1.1 项目背景
根据《网络安全等级保护基本要求》(GB/T 22239-2019)，需要开发一款Linux系统等保基线核查工具，用于自动化检查Linux服务器是否符合等级保护基线安全要求。

### 1.2 项目目标
- 提供图形化界面的Linux安全基线检查工具
- 支持SSH远程连接执行检查，无需在目标服务器安装客户端
- 支持多种Linux发行版（CentOS、Ubuntu、RHEL等）
- 支持批量服务器检查和管理
- 自动化执行等保基线检查项目
- 生成标准化的检查报告
- 提供安全加固建议

### 1.3 技术选型
- **开发语言**: Go 1.21+
- **GUI框架**: Fyne v2
- **SSH连接**: golang.org/x/crypto/ssh
- **数据库**: SQLite (github.com/mattn/go-sqlite3)
- **配置方式**: 内置规则和配置
- **报告格式**: HTML、PDF、Excel
- **目标平台**: Linux x64、Windows x64、macOS

## 2. 功能需求

### 2.1 核心功能模块

#### 2.1.1 远程连接管理模块
- **服务器管理**
  - 服务器信息管理（IP、端口、用户名等）
  - 服务器分组和标签管理
  - 批量导入/导出服务器列表
  - 服务器连接状态监控
  - 连接测试和诊断功能

- **SSH连接管理**
  - 支持密码认证和密钥认证
  - SSH密钥管理（生成、导入、导出）
  - 连接池管理和复用
  - 自动重连机制
  - 连接超时和错误处理

- **批量操作支持**
  - 批量服务器检查
  - 并发连接控制
  - 检查进度实时显示
  - 失败重试机制
  - 操作日志记录

#### 2.1.2 基线检查引擎
- **账户安全检查**
  - 密码策略检查（复杂度、有效期、历史密码等）
  - 账户锁定策略
  - 特权账户管理
  - 空密码账户检查
  - 用户权限检查

- **系统配置检查**
  - 内核参数安全配置
  - 系统服务安全配置
  - 启动项检查
  - 时间同步配置
  - 系统资源限制

- **网络安全检查**
  - 防火墙配置检查
  - 网络服务安全配置
  - 端口开放状态检查
  - SSH配置安全检查
  - 网络参数配置

- **文件系统检查**
  - 关键文件权限检查
  - 文件完整性检查
  - 敏感目录权限
  - 临时文件清理策略
  - 磁盘分区安全

- **日志审计检查**
  - 系统日志配置
  - 审计策略配置
  - 日志轮转配置
  - 远程日志配置
  - 日志完整性保护

- **安全更新检查**
  - 系统补丁状态
  - 软件版本检查
  - 安全更新策略
  - 漏洞扫描结果

#### 2.1.3 数据管理模块
- 内置检查规则管理
- 内置基线标准模板
- 服务器信息数据库存储
- 检查结果历史记录
- 用户配置持久化
- 数据库备份和恢复

#### 2.1.4 报告生成模块
- 检查结果汇总
- 风险等级评估
- 加固建议生成
- 多格式报告导出
- 历史报告管理

#### 2.1.5 GUI界面模块
- 主界面设计
- 检查配置界面
- 实时进度显示
- 结果查看界面
- 设置管理界面

### 2.2 用户界面需求

#### 2.2.1 主界面布局
```
┌─────────────────────────────────────────────────────────┐
│ 菜单栏: 文件 | 服务器 | 检查 | 报告 | 设置 | 帮助            │
├─────────────┬───────────────────────────┬─────────────────┤
│             │                           │                 │
│  导航栏     │      主要工作区域          │   信息面板      │
│             │                           │                 │
│ □ 仪表板    │                           │ 服务器状态      │
│ □ 服务器管理│                           │ 检查结果统计    │
│ □ 账户安全  │                           │ 风险等级分布    │
│ □ 系统配置  │                           │ 建议措施        │
│ □ 网络安全  │                           │ 详细信息        │
│ □ 文件系统  │                           │                 │
│ □ 日志审计  │                           │                 │
│ □ 安全更新  │                           │                 │
│ □ 报告管理  │                           │                 │
│ □ 设置      │                           │                 │
│             │                           │                 │
├─────────────┴───────────────────────────┴─────────────────┤
│ 状态栏: 就绪 | 连接状态 | 进度条 | 最后检查时间              │
└─────────────────────────────────────────────────────────┘
```

#### 2.2.2 主要功能页面
1. **仪表板页面**
   - 服务器连接状态总览
   - 系统安全状况总览
   - 风险等级饼图
   - 检查项通过率
   - 最近检查历史

2. **服务器管理页面**
   - 服务器列表管理
   - 服务器分组和标签
   - 连接配置和测试
   - 批量操作功能
   - SSH密钥管理

3. **检查配置页面**
   - 目标服务器选择
   - 检查项目选择
   - 检查策略配置
   - 自定义规则设置
   - 批量检查计划配置

4. **检查执行页面**
   - 服务器连接状态
   - 实时进度条（按服务器分组）
   - 当前检查项显示
   - 检查结果实时更新
   - 错误信息和重试提示

5. **报告查看页面**
   - 按服务器分组的检查结果
   - 详细结果查看
   - 风险等级筛选
   - 批量报告导出功能
   - 历史报告对比

6. **设置页面**
   - SSH连接配置
   - 基线标准选择
   - 工具配置参数
   - 报告模板设置
   - 语言和主题设置

## 3. 非功能需求

### 3.1 性能要求
- 支持同时检查1000+检查项
- 支持管理500+台服务器
- 单次完整检查时间不超过10分钟
- 内存占用不超过512MB
- SQLite数据库文件大小控制在100MB以内
- 支持并发检查以提高效率

### 3.2 可用性要求
- 界面简洁直观，易于操作
- 提供详细的帮助文档
- 支持中英文界面
- 错误信息清晰明确

### 3.3 可靠性要求
- 检查过程异常中断后可恢复
- SQLite数据库事务保证数据完整性
- 自动数据库备份和恢复功能
- 系统资源占用合理
- 异常情况处理完善
- 数据库损坏自动修复

### 3.4 安全性要求
- 检查过程不影响系统正常运行
- 敏感信息加密存储
- 操作日志记录
- 权限控制机制

### 3.5 兼容性要求
- 支持主流Linux发行版
- 支持不同内核版本
- 向后兼容旧版本配置
- 跨架构支持（x64、ARM64）

## 4. 技术架构设计

### 4.1 整体架构
```
┌─────────────────────────────────────────────────────────┐
│                   GUI层 (Fyne)                         │
├─────────────────────────────────────────────────────────┤
│                   业务逻辑层                             │
│  ┌─────────────┬─────────────┬─────────────┬───────────┐ │
│  │ 检查引擎    │ 配置管理    │ 报告生成    │ 任务调度  │ │
│  └─────────────┴─────────────┴─────────────┴───────────┘ │
├─────────────────────────────────────────────────────────┤
│                   连接层                                 │
│  ┌─────────────┬─────────────┬─────────────┬───────────┐ │
│  │ SSH连接池   │ 命令执行    │ 文件传输    │ 状态监控  │ │
│  └─────────────┴─────────────┴─────────────┴───────────┘ │
├─────────────────────────────────────────────────────────┤
│                   数据访问层                             │
│  ┌─────────────┬─────────────┬─────────────┬───────────┐ │
│  │ 服务器管理  │ 配置读写    │ 报告存储    │ 日志记录  │ │
│  └─────────────┴─────────────┴─────────────┴───────────┘ │
├─────────────────────────────────────────────────────────┤
│                   网络层                                 │
│              SSH协议 + 目标Linux服务器                  │
└─────────────────────────────────────────────────────────┘
```

### 4.2 项目目录结构
```
linux-security-checker/
├── cmd/
│   └── main.go                 # 程序入口
├── internal/
│   ├── gui/                    # GUI相关
│   │   ├── app.go             # 应用主框架
│   │   ├── dashboard.go       # 仪表板页面
│   │   ├── servers.go         # 服务器管理页面
│   │   ├── checker.go         # 检查页面
│   │   ├── report.go          # 报告页面
│   │   └── settings.go        # 设置页面
│   ├── ssh/                   # SSH连接相关
│   │   ├── client.go          # SSH客户端
│   │   ├── pool.go            # 连接池管理
│   │   ├── executor.go        # 远程命令执行
│   │   └── auth.go            # 认证管理
│   ├── database/              # 数据库相关
│   │   ├── db.go              # 数据库连接管理
│   │   ├── models.go          # 数据模型
│   │   ├── migrations.go      # 数据库迁移
│   │   └── repository.go      # 数据访问层
│   ├── checker/               # 检查引擎
│   │   ├── engine.go          # 检查引擎核心
│   │   ├── account.go         # 账户安全检查
│   │   ├── system.go          # 系统配置检查
│   │   ├── network.go         # 网络安全检查
│   │   ├── filesystem.go      # 文件系统检查
│   │   ├── audit.go           # 日志审计检查
│   │   └── update.go          # 安全更新检查
│   ├── rules/                 # 内置规则定义
│   │   ├── builtin.go         # 内置规则集合
│   │   ├── level2.go          # 等保二级规则
│   │   ├── level3.go          # 等保三级规则
│   │   └── custom.go          # 自定义规则
│   ├── report/                # 报告生成
│   │   ├── generator.go       # 报告生成器
│   │   ├── html.go           # HTML报告
│   │   ├── pdf.go            # PDF报告
│   │   └── excel.go          # Excel报告
│   ├── server/                # 服务器管理
│   │   ├── manager.go         # 服务器管理器
│   │   ├── group.go           # 服务器分组
│   │   └── monitor.go         # 状态监控
│   └── common/                # 公共组件
│       ├── logger.go          # 日志组件
│       ├── utils.go           # 工具函数
│       └── types.go           # 数据类型
├── data/                      # 数据目录
│   ├── security_checker.db   # SQLite数据库文件
│   └── backups/              # 数据库备份目录
├── templates/                 # 报告模板
│   ├── html/                 # HTML模板
│   └── pdf/                  # PDF模板
├── assets/                    # 资源文件
│   ├── icons/                # 图标文件
│   └── i18n/                 # 国际化文件
├── docs/                      # 文档
├── scripts/                   # 构建脚本
├── go.mod
├── go.sum
├── Makefile
└── README.md

### 4.3 核心组件设计

#### 4.3.1 SSH连接管理 (SSH Connection Manager)
```go
type SSHClient struct {
    host       string
    port       int
    username   string
    authMethod ssh.AuthMethod
    client     *ssh.Client
    session    *ssh.Session
}

type ConnectionPool struct {
    connections map[string]*SSHClient
    maxConn     int
    timeout     time.Duration
    mutex       sync.RWMutex
}

type RemoteExecutor struct {
    pool    *ConnectionPool
    logger  *Logger
}
```

#### 4.3.2 检查引擎 (Checker Engine)
```go
type CheckEngine struct {
    rules     []CheckRule
    executor  *RemoteExecutor
    reporter  *Reporter
    config    *Config
}

type CheckRule struct {
    ID          string
    Name        string
    Category    string
    Level       string
    Description string
    Command     string
    Expected    interface{}
    Severity    string
}

type CheckResult struct {
    ServerID    string
    RuleID      string
    Status      string  // PASS, FAIL, SKIP, ERROR
    ActualValue interface{}
    Message     string
    Suggestion  string
    Timestamp   time.Time
}
```

#### 4.3.3 服务器管理 (Server Manager)
```go
type Server struct {
    ID          string
    Name        string
    Host        string
    Port        int
    Username    string
    AuthType    string  // password, key
    Password    string  // 加密存储
    KeyPath     string
    Groups      []string
    Tags        []string
    Status      string  // online, offline, error
    LastCheck   time.Time
}

type ServerManager struct {
    servers    map[string]*Server
    groups     map[string][]string
    configPath string
    mutex      sync.RWMutex
}
```

#### 4.3.4 数据库管理 (Database Manager)
```go
type DatabaseManager struct {
    db     *sql.DB
    dbPath string
}

type ServerModel struct {
    ID          int       `db:"id"`
    Name        string    `db:"name"`
    Host        string    `db:"host"`
    Port        int       `db:"port"`
    Username    string    `db:"username"`
    AuthType    string    `db:"auth_type"`
    Password    string    `db:"password"` // 加密存储
    KeyPath     string    `db:"key_path"`
    Groups      string    `db:"groups"`   // JSON格式
    Tags        string    `db:"tags"`     // JSON格式
    Status      string    `db:"status"`
    CreatedAt   time.Time `db:"created_at"`
    UpdatedAt   time.Time `db:"updated_at"`
}

type CheckResultModel struct {
    ID          int       `db:"id"`
    ServerID    int       `db:"server_id"`
    RuleID      string    `db:"rule_id"`
    Status      string    `db:"status"`
    ActualValue string    `db:"actual_value"`
    Expected    string    `db:"expected"`
    Message     string    `db:"message"`
    Suggestion  string    `db:"suggestion"`
    CreatedAt   time.Time `db:"created_at"`
}
```

#### 4.3.5 报告生成器 (Report Generator)
```go
type ReportGenerator struct {
    templatePath string
    outputPath   string
    formatter    Formatter
}

type Report struct {
    Summary     Summary
    Servers     []ServerReport
    Categories  []CategoryResult
    Details     []CheckResult
    Timestamp   time.Time
}

type ServerReport struct {
    ServerID    string
    ServerName  string
    Status      string
    Summary     Summary
    SystemInfo  SystemInfo
    Results     []CheckResult
}
```

## 5. 开发计划

### 5.1 开发阶段

#### 第一阶段：基础框架搭建 (2周)
- [x] 项目初始化和目录结构
- [x] Go模块和依赖管理
- [x] 基础GUI框架搭建
- [x] 配置管理模块
- [x] 日志系统实现

#### 第二阶段：SSH连接和服务器管理 (2周)
- [x] SSH连接客户端实现
- [x] 连接池管理
- [x] 远程命令执行器
- [x] 服务器管理模块
- [x] 认证管理（密码/密钥）

#### 第三阶段：检查引擎开发 (3周)
- [x] 检查引擎核心框架
- [x] 账户安全检查模块
- [x] 系统配置检查模块
- [x] 网络安全检查模块
- [x] 文件系统检查模块
- [x] 日志审计检查模块

#### 第四阶段：GUI界面开发 (3周)
- [x] 主界面布局设计
- [x] 仪表板页面实现
- [x] 服务器管理页面
- [x] 检查配置页面
- [x] 检查执行页面
- [x] 结果查看页面

#### 第五阶段：报告系统开发 (1周)
- [x] 报告生成引擎
- [x] 多服务器报告模板
- [x] HTML报告模板
- [x] PDF报告生成
- [x] Excel报告导出

#### 第六阶段：测试和优化 (2周)
- [x] SSH连接测试
- [x] 多服务器并发测试
- [x] 单元测试编写
- [x] 集成测试
- [x] 性能优化
- [x] 用户体验优化

#### 第七阶段：文档和发布 (1周)
- [x] 用户手册编写
- [x] 安装部署文档
- [x] SSH配置指南
- [x] 版本打包发布
- [x] 培训材料准备

### 5.2 里程碑节点
- **M1**: 基础框架完成 (第2周)
- **M2**: SSH连接功能完成 (第4周)
- **M3**: 核心检查功能完成 (第7周)
- **M4**: GUI界面完成 (第10周)
- **M5**: 完整功能实现 (第11周)
- **M6**: 测试完成 (第13周)
- **M7**: 正式发布 (第14周)

## 6. 风险评估

### 6.1 技术风险
- **SSH连接稳定性**: 网络不稳定可能导致连接中断
- **认证安全性**: SSH密钥和密码的安全存储和传输
- **并发连接限制**: 大量服务器并发连接可能受到限制
- **Fyne框架兼容性**: 不同操作系统GUI兼容性问题
- **系统权限**: 某些检查项需要目标服务器root权限
- **性能问题**: 大量检查项并发执行可能影响网络和系统性能

### 6.2 业务风险
- **需求变更**: 等保标准更新导致检查项变更
- **用户接受度**: 用户对新工具的接受和使用习惯

### 6.3 风险应对措施
- 实现连接重试和故障转移机制
- 采用加密存储和安全传输协议
- 设计连接池和限流机制
- 充分测试不同操作系统环境
- 实现权限检查和提示机制
- 优化并发控制和资源管理
- 设计灵活的配置和扩展机制

## 7. 验收标准

### 7.1 功能验收
- 支持完整的等保基线检查项目
- 内置规则覆盖等保二级和三级要求
- GUI界面功能完整且易用
- SQLite数据库稳定可靠
- 服务器管理功能完善
- 报告生成准确且格式规范

### 7.2 性能验收
- 完整检查时间不超过10分钟
- 内存占用不超过512MB
- 支持1000+检查项并发执行
- 界面响应时间不超过2秒

### 7.3 质量验收
- 代码覆盖率达到80%以上
- 无严重安全漏洞
- 通过主流Linux发行版兼容性测试
- 用户手册完整且易懂

---

**文档版本**: v1.0
**编写日期**: 2025-08-20
**编写人员**: AI助手
**审核状态**: 待审核
```
