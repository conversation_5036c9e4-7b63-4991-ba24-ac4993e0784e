package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

// DatabaseManager 数据库管理器
type DatabaseManager struct {
	db     *sql.DB
	dbPath string
}

// NewDatabaseManager 创建数据库管理器
func NewDatabaseManager(dbPath string) (*DatabaseManager, error) {
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %v", err)
	}

	dm := &DatabaseManager{
		db:     db,
		dbPath: dbPath,
	}

	// 初始化数据库表
	if err := dm.initTables(); err != nil {
		return nil, fmt.Errorf("failed to initialize tables: %v", err)
	}

	return dm, nil
}

// initTables 初始化数据库表
func (dm *DatabaseManager) initTables() error {
	// 服务器表
	serverTable := `
	CREATE TABLE IF NOT EXISTS servers (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT NOT NULL,
		host TEXT NOT NULL,
		port INTEGER NOT NULL DEFAULT 22,
		username TEXT NOT NULL,
		auth_type TEXT NOT NULL DEFAULT 'password',
		password TEXT,
		key_path TEXT,
		groups TEXT DEFAULT '[]',
		tags TEXT DEFAULT '[]',
		status TEXT DEFAULT 'offline',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	// 检查结果表
	resultTable := `
	CREATE TABLE IF NOT EXISTS check_results (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		server_id INTEGER NOT NULL,
		rule_id TEXT NOT NULL,
		status TEXT NOT NULL,
		actual_value TEXT,
		expected TEXT,
		message TEXT,
		suggestion TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (server_id) REFERENCES servers (id)
	);`

	// 检查会话表
	sessionTable := `
	CREATE TABLE IF NOT EXISTS check_sessions (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		name TEXT NOT NULL,
		server_ids TEXT NOT NULL,
		baseline_level TEXT NOT NULL,
		status TEXT DEFAULT 'pending',
		total_rules INTEGER DEFAULT 0,
		passed_rules INTEGER DEFAULT 0,
		failed_rules INTEGER DEFAULT 0,
		error_rules INTEGER DEFAULT 0,
		started_at DATETIME,
		completed_at DATETIME,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	tables := []string{serverTable, resultTable, sessionTable}
	for _, table := range tables {
		if _, err := dm.db.Exec(table); err != nil {
			return fmt.Errorf("failed to create table: %v", err)
		}
	}

	return nil
}

// ServerModel 服务器数据模型
type ServerModel struct {
	ID        int       `json:"id"`
	Name      string    `json:"name"`
	Host      string    `json:"host"`
	Port      int       `json:"port"`
	Username  string    `json:"username"`
	AuthType  string    `json:"auth_type"`
	Password  string    `json:"password"`
	KeyPath   string    `json:"key_path"`
	Groups    []string  `json:"groups"`
	Tags      []string  `json:"tags"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// AddServer 添加服务器
func (dm *DatabaseManager) AddServer(server *ServerModel) error {
	groupsJSON, _ := json.Marshal(server.Groups)
	tagsJSON, _ := json.Marshal(server.Tags)

	query := `
		INSERT INTO servers (name, host, port, username, auth_type, password, key_path, groups, tags, status)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := dm.db.Exec(query,
		server.Name, server.Host, server.Port, server.Username,
		server.AuthType, server.Password, server.KeyPath,
		string(groupsJSON), string(tagsJSON), server.Status)

	if err != nil {
		return fmt.Errorf("failed to add server: %v", err)
	}

	id, _ := result.LastInsertId()
	server.ID = int(id)
	return nil
}

// GetServers 获取所有服务器
func (dm *DatabaseManager) GetServers() ([]*ServerModel, error) {
	query := `SELECT id, name, host, port, username, auth_type, password, key_path, groups, tags, status, created_at, updated_at FROM servers`
	
	rows, err := dm.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query servers: %v", err)
	}
	defer rows.Close()

	var servers []*ServerModel
	for rows.Next() {
		server := &ServerModel{}
		var groupsJSON, tagsJSON string

		err := rows.Scan(&server.ID, &server.Name, &server.Host, &server.Port,
			&server.Username, &server.AuthType, &server.Password, &server.KeyPath,
			&groupsJSON, &tagsJSON, &server.Status, &server.CreatedAt, &server.UpdatedAt)

		if err != nil {
			return nil, fmt.Errorf("failed to scan server: %v", err)
		}

		json.Unmarshal([]byte(groupsJSON), &server.Groups)
		json.Unmarshal([]byte(tagsJSON), &server.Tags)

		servers = append(servers, server)
	}

	return servers, nil
}

// CheckResultModel 检查结果数据模型
type CheckResultModel struct {
	ID          int       `json:"id"`
	ServerID    int       `json:"server_id"`
	RuleID      string    `json:"rule_id"`
	Status      string    `json:"status"`
	ActualValue string    `json:"actual_value"`
	Expected    string    `json:"expected"`
	Message     string    `json:"message"`
	Suggestion  string    `json:"suggestion"`
	CreatedAt   time.Time `json:"created_at"`
}

// SaveCheckResult 保存检查结果
func (dm *DatabaseManager) SaveCheckResult(result *CheckResultModel) error {
	query := `
		INSERT INTO check_results (server_id, rule_id, status, actual_value, expected, message, suggestion)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	_, err := dm.db.Exec(query,
		result.ServerID, result.RuleID, result.Status,
		result.ActualValue, result.Expected, result.Message, result.Suggestion)

	return err
}

// GetCheckResults 获取检查结果
func (dm *DatabaseManager) GetCheckResults(serverID int, limit int) ([]*CheckResultModel, error) {
	query := `
		SELECT id, server_id, rule_id, status, actual_value, expected, message, suggestion, created_at
		FROM check_results
		WHERE server_id = ?
		ORDER BY created_at DESC
		LIMIT ?
	`

	rows, err := dm.db.Query(query, serverID, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []*CheckResultModel
	for rows.Next() {
		result := &CheckResultModel{}
		err := rows.Scan(&result.ID, &result.ServerID, &result.RuleID,
			&result.Status, &result.ActualValue, &result.Expected,
			&result.Message, &result.Suggestion, &result.CreatedAt)

		if err != nil {
			return nil, err
		}

		results = append(results, result)
	}

	return results, nil
}

// 内置检查规则定义
type CheckRule struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Category    string `json:"category"`
	Level       string `json:"level"`
	Description string `json:"description"`
	Command     string `json:"command"`
	Expected    string `json:"expected"`
	Severity    string `json:"severity"`
	Suggestion  string `json:"suggestion"`
}

// BuiltinRules 内置规则管理器
type BuiltinRules struct {
	rules map[string][]CheckRule
}

// NewBuiltinRules 创建内置规则管理器
func NewBuiltinRules() *BuiltinRules {
	br := &BuiltinRules{
		rules: make(map[string][]CheckRule),
	}
	br.initRules()
	return br
}

// initRules 初始化内置规则
func (br *BuiltinRules) initRules() {
	// 等保二级规则
	level2Rules := []CheckRule{
		{
			ID:          "L2_ACCOUNT_001",
			Name:        "检查空密码账户",
			Category:    "账户安全",
			Level:       "level2",
			Description: "检查系统中是否存在空密码账户",
			Command:     "awk -F: '($2 == \"\") {print $1}' /etc/shadow",
			Expected:    "",
			Severity:    "high",
			Suggestion:  "为所有账户设置强密码",
		},
		{
			ID:          "L2_ACCOUNT_002",
			Name:        "检查密码最小长度",
			Category:    "账户安全",
			Level:       "level2",
			Description: "检查密码最小长度配置",
			Command:     "grep '^PASS_MIN_LEN' /etc/login.defs | awk '{print $2}'",
			Expected:    "8",
			Severity:    "medium",
			Suggestion:  "设置密码最小长度为8位",
		},
		{
			ID:          "L2_SYSTEM_001",
			Name:        "检查SSH配置",
			Category:    "系统配置",
			Level:       "level2",
			Description: "检查SSH是否禁用root登录",
			Command:     "grep '^PermitRootLogin' /etc/ssh/sshd_config | awk '{print $2}'",
			Expected:    "no",
			Severity:    "high",
			Suggestion:  "禁用SSH root登录",
		},
		{
			ID:          "L2_NETWORK_001",
			Name:        "检查防火墙状态",
			Category:    "网络安全",
			Level:       "level2",
			Description: "检查防火墙是否启用",
			Command:     "systemctl is-active firewalld || systemctl is-active ufw",
			Expected:    "active",
			Severity:    "high",
			Suggestion:  "启用系统防火墙",
		},
	}

	// 等保三级规则（包含二级规则）
	level3Rules := append(level2Rules, []CheckRule{
		{
			ID:          "L3_ACCOUNT_001",
			Name:        "检查账户锁定策略",
			Category:    "账户安全",
			Level:       "level3",
			Description: "检查账户锁定策略配置",
			Command:     "grep '^FAILLOG_ENAB' /etc/login.defs | awk '{print $2}'",
			Expected:    "yes",
			Severity:    "medium",
			Suggestion:  "启用账户锁定策略",
		},
		{
			ID:          "L3_AUDIT_001",
			Name:        "检查审计服务",
			Category:    "日志审计",
			Level:       "level3",
			Description: "检查审计服务是否启用",
			Command:     "systemctl is-active auditd",
			Expected:    "active",
			Severity:    "high",
			Suggestion:  "启用审计服务",
		},
	}...)

	br.rules["level2"] = level2Rules
	br.rules["level3"] = level3Rules
}

// GetRulesByLevel 根据等级获取规则
func (br *BuiltinRules) GetRulesByLevel(level string) []CheckRule {
	return br.rules[level]
}

// GetRulesByCategory 根据分类获取规则
func (br *BuiltinRules) GetRulesByCategory(level, category string) []CheckRule {
	rules := br.rules[level]
	var filtered []CheckRule

	for _, rule := range rules {
		if rule.Category == category {
			filtered = append(filtered, rule)
		}
	}

	return filtered
}

// GetAllCategories 获取所有分类
func (br *BuiltinRules) GetAllCategories() []string {
	categories := make(map[string]bool)
	
	for _, rules := range br.rules {
		for _, rule := range rules {
			categories[rule.Category] = true
		}
	}

	var result []string
	for category := range categories {
		result = append(result, category)
	}

	return result
}

// 示例使用
func main() {
	// 初始化数据库
	db, err := NewDatabaseManager("./security_checker.db")
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// 添加服务器示例
	server := &ServerModel{
		Name:     "测试服务器",
		Host:     "*************",
		Port:     22,
		Username: "root",
		AuthType: "password",
		Password: "password123",
		Groups:   []string{"生产环境", "Web服务器"},
		Tags:     []string{"CentOS", "重要"},
		Status:   "online",
	}

	err = db.AddServer(server)
	if err != nil {
		log.Printf("Failed to add server: %v", err)
	} else {
		log.Printf("Server added with ID: %d", server.ID)
	}

	// 获取所有服务器
	servers, err := db.GetServers()
	if err != nil {
		log.Printf("Failed to get servers: %v", err)
	} else {
		log.Printf("Found %d servers", len(servers))
	}

	// 初始化内置规则
	rules := NewBuiltinRules()
	level2Rules := rules.GetRulesByLevel("level2")
	log.Printf("Level 2 rules count: %d", len(level2Rules))

	// 保存检查结果示例
	result := &CheckResultModel{
		ServerID:    server.ID,
		RuleID:      "L2_ACCOUNT_001",
		Status:      "PASS",
		ActualValue: "",
		Expected:    "",
		Message:     "No empty password accounts found",
		Suggestion:  "",
	}

	err = db.SaveCheckResult(result)
	if err != nil {
		log.Printf("Failed to save check result: %v", err)
	} else {
		log.Println("Check result saved successfully")
	}
}
