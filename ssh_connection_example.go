package main

import (
	"fmt"
	"log"
	"net"
	"sync"
	"time"

	"golang.org/x/crypto/ssh"
)

// Server 服务器信息结构
type Server struct {
	ID       string `yaml:"id"`
	Name     string `yaml:"name"`
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Username string `yaml:"username"`
	AuthType string `yaml:"auth_type"` // password, key
	Password string `yaml:"password"`  // 加密存储
	KeyPath  string `yaml:"key_path"`
	Groups   []string `yaml:"groups"`
	Tags     []string `yaml:"tags"`
	Status   string   `yaml:"-"` // online, offline, error
}

// SSHClient SSH客户端封装
type SSHClient struct {
	server *Server
	client *ssh.Client
	mutex  sync.Mutex
}

// ConnectionPool SSH连接池
type ConnectionPool struct {
	connections map[string]*SSHClient
	maxConn     int
	timeout     time.Duration
	mutex       sync.RWMutex
}

// NewConnectionPool 创建连接池
func NewConnectionPool(maxConn int, timeout time.Duration) *ConnectionPool {
	return &ConnectionPool{
		connections: make(map[string]*SSHClient),
		maxConn:     maxConn,
		timeout:     timeout,
	}
}

// Connect 连接到服务器
func (pool *ConnectionPool) Connect(server *Server) (*SSHClient, error) {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	// 检查是否已存在连接
	if client, exists := pool.connections[server.ID]; exists {
		if client.IsConnected() {
			return client, nil
		}
		// 清理无效连接
		delete(pool.connections, server.ID)
	}

	// 创建新连接
	client, err := pool.createConnection(server)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %v", server.Host, err)
	}

	pool.connections[server.ID] = client
	return client, nil
}

// createConnection 创建SSH连接
func (pool *ConnectionPool) createConnection(server *Server) (*SSHClient, error) {
	var authMethod ssh.AuthMethod

	// 根据认证类型选择认证方法
	switch server.AuthType {
	case "password":
		authMethod = ssh.Password(server.Password)
	case "key":
		key, err := ssh.ParsePrivateKeyFile(server.KeyPath)
		if err != nil {
			return nil, fmt.Errorf("failed to parse private key: %v", err)
		}
		authMethod = ssh.PublicKeys(key)
	default:
		return nil, fmt.Errorf("unsupported auth type: %s", server.AuthType)
	}

	// SSH客户端配置
	config := &ssh.ClientConfig{
		User: server.Username,
		Auth: []ssh.AuthMethod{authMethod},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 生产环境应该验证主机密钥
		Timeout:         pool.timeout,
	}

	// 建立连接
	addr := fmt.Sprintf("%s:%d", server.Host, server.Port)
	client, err := ssh.Dial("tcp", addr, config)
	if err != nil {
		return nil, err
	}

	return &SSHClient{
		server: server,
		client: client,
	}, nil
}

// IsConnected 检查连接状态
func (c *SSHClient) IsConnected() bool {
	if c.client == nil {
		return false
	}

	// 通过发送心跳检查连接
	_, _, err := c.client.SendRequest("<EMAIL>", true, nil)
	return err == nil
}

// ExecuteCommand 执行远程命令
func (c *SSHClient) ExecuteCommand(command string) (string, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	session, err := c.client.NewSession()
	if err != nil {
		return "", fmt.Errorf("failed to create session: %v", err)
	}
	defer session.Close()

	output, err := session.CombinedOutput(command)
	if err != nil {
		return "", fmt.Errorf("command execution failed: %v", err)
	}

	return string(output), nil
}

// Close 关闭连接
func (c *SSHClient) Close() error {
	if c.client != nil {
		return c.client.Close()
	}
	return nil
}

// RemoteExecutor 远程命令执行器
type RemoteExecutor struct {
	pool   *ConnectionPool
	logger *log.Logger
}

// NewRemoteExecutor 创建远程执行器
func NewRemoteExecutor(pool *ConnectionPool, logger *log.Logger) *RemoteExecutor {
	return &RemoteExecutor{
		pool:   pool,
		logger: logger,
	}
}

// ExecuteOnServer 在指定服务器上执行命令
func (re *RemoteExecutor) ExecuteOnServer(server *Server, command string) (string, error) {
	client, err := re.pool.Connect(server)
	if err != nil {
		re.logger.Printf("Failed to connect to server %s: %v", server.Name, err)
		return "", err
	}

	re.logger.Printf("Executing command on %s: %s", server.Name, command)
	output, err := client.ExecuteCommand(command)
	if err != nil {
		re.logger.Printf("Command failed on %s: %v", server.Name, err)
		return "", err
	}

	return output, nil
}

// ExecuteOnMultipleServers 在多个服务器上并发执行命令
func (re *RemoteExecutor) ExecuteOnMultipleServers(servers []*Server, command string) map[string]ExecutionResult {
	results := make(map[string]ExecutionResult)
	var wg sync.WaitGroup
	var mutex sync.Mutex

	for _, server := range servers {
		wg.Add(1)
		go func(srv *Server) {
			defer wg.Done()

			output, err := re.ExecuteOnServer(srv, command)
			
			mutex.Lock()
			results[srv.ID] = ExecutionResult{
				ServerID: srv.ID,
				Output:   output,
				Error:    err,
				Timestamp: time.Now(),
			}
			mutex.Unlock()
		}(server)
	}

	wg.Wait()
	return results
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	ServerID  string
	Output    string
	Error     error
	Timestamp time.Time
}

// CheckRule 检查规则
type CheckRule struct {
	ID          string `yaml:"id"`
	Name        string `yaml:"name"`
	Category    string `yaml:"category"`
	Level       string `yaml:"level"`
	Description string `yaml:"description"`
	Command     string `yaml:"command"`
	Expected    string `yaml:"expected"`
	Severity    string `yaml:"severity"`
}

// CheckResult 检查结果
type CheckResult struct {
	ServerID    string    `json:"server_id"`
	RuleID      string    `json:"rule_id"`
	Status      string    `json:"status"` // PASS, FAIL, SKIP, ERROR
	ActualValue string    `json:"actual_value"`
	Expected    string    `json:"expected"`
	Message     string    `json:"message"`
	Suggestion  string    `json:"suggestion"`
	Timestamp   time.Time `json:"timestamp"`
}

// SecurityChecker 安全检查器
type SecurityChecker struct {
	executor *RemoteExecutor
	rules    []CheckRule
}

// NewSecurityChecker 创建安全检查器
func NewSecurityChecker(executor *RemoteExecutor, rules []CheckRule) *SecurityChecker {
	return &SecurityChecker{
		executor: executor,
		rules:    rules,
	}
}

// CheckServer 检查单个服务器
func (sc *SecurityChecker) CheckServer(server *Server) []CheckResult {
	var results []CheckResult

	for _, rule := range sc.rules {
		result := sc.executeRule(server, rule)
		results = append(results, result)
	}

	return results
}

// executeRule 执行单个检查规则
func (sc *SecurityChecker) executeRule(server *Server, rule CheckRule) CheckResult {
	output, err := sc.executor.ExecuteOnServer(server, rule.Command)
	
	result := CheckResult{
		ServerID:  server.ID,
		RuleID:    rule.ID,
		Expected:  rule.Expected,
		Timestamp: time.Now(),
	}

	if err != nil {
		result.Status = "ERROR"
		result.Message = fmt.Sprintf("Command execution failed: %v", err)
		return result
	}

	result.ActualValue = output

	// 简单的字符串匹配检查（实际应用中需要更复杂的逻辑）
	if output == rule.Expected {
		result.Status = "PASS"
		result.Message = "Check passed"
	} else {
		result.Status = "FAIL"
		result.Message = "Check failed"
		result.Suggestion = fmt.Sprintf("Expected: %s, Got: %s", rule.Expected, output)
	}

	return result
}

// 示例使用
func main() {
	// 创建连接池
	pool := NewConnectionPool(10, 30*time.Second)

	// 创建日志器
	logger := log.New(log.Writer(), "[SSH] ", log.LstdFlags)

	// 创建远程执行器
	executor := NewRemoteExecutor(pool, logger)

	// 示例服务器配置
	server := &Server{
		ID:       "server1",
		Name:     "Test Server",
		Host:     "*************",
		Port:     22,
		Username: "root",
		AuthType: "password",
		Password: "password123",
	}

	// 执行命令示例
	output, err := executor.ExecuteOnServer(server, "uname -a")
	if err != nil {
		log.Printf("Error: %v", err)
	} else {
		log.Printf("Output: %s", output)
	}
}
